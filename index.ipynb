{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "jupyter: python3\n", "bibliography: references.bib\n", "biblatex: true\n", "latex-auto-install: true\n", "format:\n", "  pdf:\n", "    pdf-engine: lualatex\n", "    include-in-header:\n", "      text: |\n", "        \\usepackage{KUstyle}\n", "        \\usepackage{microtype}\n", "        \\usepackage{setspace}\n", "        \\usepackage{amsmath,amssymb}\n", "        \\usepackage{booktabs}\n", "        \\usepackage{longtable}\n", "        \\onehalfspacing\n", "        % Use Latin Modern (similar to Computer Modern used in Overleaf)\n", "        \\setmainfont{Latin Modern Roman}\n", "        \\setsansfont{Latin Modern Sans}\n", "        \\setmonofont{Latin Modern Mono}\n", "        % Redefine section formatting to match Overleaf style using KOMA-Script commands\n", "        \\setkomafont{chapter}{\\normalfont\\LARGE\\bfseries}\n", "        \\setkomafont{section}{\\normalfont\\Large\\bfseries}\n", "        \\setkomafont{subsection}{\\normalfont\\large\\bfseries}\n", "        % Adjust spacing for sections\n", "        \\RedeclareSectionCommand[\n", "          beforeskip=50pt,\n", "          afterskip=40pt\n", "        ]{chapter}\n", "        \\RedeclareSectionCommand[\n", "          beforeskip=3.5ex plus 1ex minus .2ex,\n", "          afterskip=2.3ex plus .2ex\n", "        ]{section}\n", "        \\RedeclareSectionCommand[\n", "          beforeskip=3.25ex plus 1ex minus .2ex,\n", "          afterskip=1.5ex plus .2ex\n", "        ]{subsection}\n", "    variables:\n", "      ptype: \"Master's Thesis\"\n", "      advisor: \"Professor Name\"\n", "      fpimage: \"Pictures/frontpage-image.jpg\"\n", "    toc: true\n", "    number-sections: true\n", "    colorlinks: true\n", "    linkcolor: black\n", "    citecolor: black\n", "    urlcolor: black\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Preface {.unnumbered}\n", "\n", "This is the preface to the PREP Rapport.\n", "\n", "\n", "## Acknowledgements {.unnumbered}\n", "\n", "Place acknowledgements here.\n"]}], "metadata": {"kernelspec": {"display_name": "prep_book", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}