% Options for packages loaded elsewhere
% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
\PassOptionsToPackage{dvipsnames,svgnames,x11names}{xcolor}
%
\documentclass[
  letterpaper,
  DIV=11,
  numbers=noendperiod]{scrartcl}
\usepackage{xcolor}
\usepackage{amsmath,amssymb}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
% Make \paragraph and \subparagraph free-standing
\makeatletter
\ifx\paragraph\undefined\else
  \let\oldparagraph\paragraph
  \renewcommand{\paragraph}{
    \@ifstar
      \xxxParagraphStar
      \xxxParagraphNoStar
  }
  \newcommand{\xxxParagraphStar}[1]{\oldparagraph*{#1}\mbox{}}
  \newcommand{\xxxParagraphNoStar}[1]{\oldparagraph{#1}\mbox{}}
\fi
\ifx\subparagraph\undefined\else
  \let\oldsubparagraph\subparagraph
  \renewcommand{\subparagraph}{
    \@ifstar
      \xxxSubParagraphStar
      \xxxSubParagraphNoStar
  }
  \newcommand{\xxxSubParagraphStar}[1]{\oldsubparagraph*{#1}\mbox{}}
  \newcommand{\xxxSubParagraphNoStar}[1]{\oldsubparagraph{#1}\mbox{}}
\fi
\makeatother

\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{241,243,245}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.40,0.45,0.13}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\BuiltInTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\ExtensionTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.28,0.35,0.67}{#1}}
\newcommand{\ImportTok}[1]{\textcolor[rgb]{0.00,0.46,0.62}{#1}}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\RegionMarkerTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.07,0.07,0.07}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}

\usepackage{longtable,booktabs,array}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother





\setlength{\emergencystretch}{3em} % prevent overfull lines

\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}



 


\KOMAoption{captions}{tableheading}
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\AtBeginDocument{%
\ifdefined\contentsname
  \renewcommand*\contentsname{Table of contents}
\else
  \newcommand\contentsname{Table of contents}
\fi
\ifdefined\listfigurename
  \renewcommand*\listfigurename{List of Figures}
\else
  \newcommand\listfigurename{List of Figures}
\fi
\ifdefined\listtablename
  \renewcommand*\listtablename{List of Tables}
\else
  \newcommand\listtablename{List of Tables}
\fi
\ifdefined\figurename
  \renewcommand*\figurename{Figure}
\else
  \newcommand\figurename{Figure}
\fi
\ifdefined\tablename
  \renewcommand*\tablename{Table}
\else
  \newcommand\tablename{Table}
\fi
}
\@ifpackageloaded{float}{}{\usepackage{float}}
\floatstyle{ruled}
\@ifundefined{c@chapter}{\newfloat{codelisting}{h}{lop}}{\newfloat{codelisting}{h}{lop}[chapter]}
\floatname{codelisting}{Listing}
\newcommand*\listoflistings{\listof{codelisting}{List of Listings}}
\makeatother
\makeatletter
\makeatother
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\@ifpackageloaded{subcaption}{}{\usepackage{subcaption}}
\makeatother
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  pdftitle={QGIS Flood Analysis Documentation},
  colorlinks=true,
  linkcolor={blue},
  filecolor={Maroon},
  citecolor={Blue},
  urlcolor={Blue},
  pdfcreator={LaTeX via pandoc}}


\title{QGIS Flood Analysis Documentation}
\author{}
\date{}
\begin{document}
\maketitle


\begin{center}\rule{0.5\linewidth}{0.5pt}\end{center}

\subsection{📸 Screenshot Guidelines}\label{screenshot-guidelines}

\subsubsection{Directory Structure:}\label{directory-structure}

\begin{verbatim}
Pictures/QGIS_workflow/
├── 01_data_loading/
├── 02_preprocessing/
├── 03_flood_calculation/
├── 04_vectorization/
├── 05_quality_control/
└── 06_export/
\end{verbatim}

\subsubsection{Image Naming:}\label{image-naming}

\begin{itemize}
\tightlist
\item
  Use descriptive names: \texttt{fill\_sinks\_dialog.png}
\item
  Include step context: \texttt{raster\_calculator\_dialog.png}
\item
  Show results: \texttt{binary\_flood\_result.png}
\end{itemize}

\subsubsection{Quality Standards:}\label{quality-standards}

\begin{itemize}
\tightlist
\item
  High resolution (1920x1080 minimum)
\item
  PNG format for UI screenshots
\item
  Include relevant toolbars and panels
\item
  Highlight important elements with arrows/circles
\end{itemize}

\subsection{Following UN-SPIDER Storm Surge Coastal Flood Modelling
Methodology}\label{following-un-spider-storm-surge-coastal-flood-modelling-methodology}

\textbf{Project:} Vejlerne Sea Level Rise Analysis \textbf{Methodology:}
UN-SPIDER Step-by-Step Guide adapted for Danish coastal conditions

\subsection{Requirements}\label{requirements}

\subsubsection{Computer Requirements:}\label{computer-requirements}

\begin{itemize}
\tightlist
\item
  QGIS 3.28 or newer
\item
  Processing power for raster analysis
\item
  Storage space for DEM and output files
\end{itemize}

\subsubsection{Required Datasets:}\label{required-datasets}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Digital Elevation Model (DEM)} - Danish DTM from
  Dataforsyningen.dk {[}@klimadat{]}:

  The DHM/Terrain model is derived from the national point cloud which
  forms the basis for the Danish Height Model (DHM). To create the
  terrain model, the point cloud is filtered to include only points
  classified as `terrain', while other classifications such as
  buildings, vegetation, and other objects are removed, resulting in the
  final DTM model that has a 0.4 meter spatial resolution with vertical
  accuracy of ±0.05-0.10m (DVR90 datum).
\item
  \textbf{Coastline vector layer} - `Kystinddeling' from DMI:

  In order to clip the DEM along the coastline I've used the layer
  `Kystinddeling' which is a part of the data from the climate variable
  `Vandstand og stormflod fordelt på Kyst' from DMI's `Klimaatlas'. The
  `Kystinddeling's layer also comes as a part of the main dataset
  'VandstandStormflodKyst' containing the data for the sea level rise
  scenarios used to define the extent/flooding for the DEM and described
  below.
\item
  \textbf{VandstandStormflodKyst} - Data on water levels and storm
  surges from DMI:

  This dataset contains climate projections for coastal water levels
  based on DMI's Climate Atlas. The data includes mean sea level rise
  (MHVS) and storm surge return periods (T10, T20, T100) for different
  emission scenarios. Data varies by coastal location. The dataset
  enables analysis of sea level rise and storm events for coastal flood
  modeling in Danish areas.
\end{enumerate}

\subsubsection{Key Settings:}\label{key-settings}

\begin{itemize}
\tightlist
\item
  \textbf{Coordinate System:} EPSG:25832 (ETRS89 / UTM zone 32N)
\item
  \textbf{Flood Scenarios:} SSP2-4,5 T10 (2070) and SSP3-7,0 T10 (2100)
\item
  \textbf{DEM Type:} DTM (Digital Terrain Model) - Danish 0,4m
  resolution
\end{itemize}

\subsection{Step 1: Data Acquisition and QGIS
Setup}\label{step-1-data-acquisition-and-qgis-setup}

Open QGIS and create new project with EPSG:25832 coordinate system.

Load Danish DTM data from Datafordeleren.

\emph{{[}Screenshot placeholder - save as:
01\_data\_loading/project\_setup.png{]}}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Load data}
\NormalTok{show\_step\_image(}\StringTok{"01\_data\_loading"}\NormalTok{, }\StringTok{"DTM\_load.png"}\NormalTok{,}
                \StringTok{"Retrieval of DTM as a WCS from Datafordeleren"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\textbf{Retrieval of DTM as a WCS from Datafordeleren}

\includegraphics[width=8.33333in,height=\textheight,keepaspectratio]{QGIS_flood_analysis_files/figure-pdf/cell-3-output-2.png}

*Image:
Pictures\QGIS\_workflow\textbackslash01\_data\_loading\DTM\_load.png*

\subsection{Step 2: DEM Preprocessing}\label{step-2-dem-preprocessing}

Fill sinks in the DEM to ensure hydrological connectivity for flood
modeling.

Use SAGA → Terrain Analysis - Hydrology → Fill Sinks (Wang \& Liu).

\emph{{[}Screenshot placeholder - save as:
02\_preprocessing/fill\_sinks\_dialog.png{]}}

Result: DTM\_Filled.tif with improved connectivity for coastal flood
analysis.

\emph{{[}Screenshot placeholder - save as:
02\_preprocessing/filled\_dem\_result.png{]}}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Display Step 2 screenshots}
\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"fill\_sinks\_dialog.png"}\NormalTok{,}
                \StringTok{"Fill Sinks algorithm dialog with parameters"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"filled\_dem\_result.png"}\NormalTok{,}
                \StringTok{"Comparison of original vs filled DEM"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

⚠️ \textbf{Image not found:} \texttt{fill\_sinks\_dialog.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}fill\_sinks\_dialog.png}

📋 \textbf{For:} Fill Sinks algorithm dialog with parameters

⚠️ \textbf{Image not found:} \texttt{filled\_dem\_result.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}filled\_dem\_result.png}

📋 \textbf{For:} Comparison of original vs filled DEM

\subsection{Step 3: Shoreline
Preprocessing}\label{step-3-shoreline-preprocessing}

Load or create coastline data for the study area.

Clip global coastline data to study area extent using `Clip Vector by
Extent'.

\emph{{[}Screenshot placeholder - save as:
02\_preprocessing/clip\_by\_extent.png{]}}

Dissolve coastline segments into unified layer using `Dissolve' tool.

\emph{{[}Screenshot placeholder - save as:
02\_preprocessing/dissolve\_coastline.png{]}}

Final preprocessed coastline ready for flood connectivity analysis.

\emph{{[}Screenshot placeholder - save as:
02\_preprocessing/final\_coastline.png{]}}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Display Step 3 screenshots}
\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"coastline\_loading.png"}\NormalTok{,}
                \StringTok{"Loading coastline/water polygon data"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"clip\_by\_extent.png"}\NormalTok{,}
                \StringTok{"Clipping coastline to study area extent"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"dissolve\_coastline.png"}\NormalTok{,}
                \StringTok{"Dissolving coastline segments into unified layer"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"02\_preprocessing"}\NormalTok{, }\StringTok{"final\_coastline.png"}\NormalTok{,}
                \StringTok{"Final preprocessed coastline ready for analysis"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

⚠️ \textbf{Image not found:} \texttt{coastline\_loading.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}coastline\_loading.png}

📋 \textbf{For:} Loading coastline/water polygon data

⚠️ \textbf{Image not found:} \texttt{clip\_by\_extent.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}clip\_by\_extent.png}

📋 \textbf{For:} Clipping coastline to study area extent

⚠️ \textbf{Image not found:} \texttt{dissolve\_coastline.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}dissolve\_coastline.png}

📋 \textbf{For:} Dissolving coastline segments into unified layer

⚠️ \textbf{Image not found:} \texttt{final\_coastline.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}02\_preprocessing\textbackslash{}final\_coastline.png}

📋 \textbf{For:} Final preprocessed coastline ready for analysis

\subsection{Step 4.1: Verify Data
Loading}\label{step-4.1-verify-data-loading}

Ensure both DEM and preprocessed coastline are loaded in QGIS project.

\emph{{[}Screenshot placeholder - save as:
03\_flood\_calculation/data\_loaded.png{]}}

\subsection{Step 4.2: Raster
Calculation}\label{step-4.2-raster-calculation}

Create binary flood mask using Raster Calculator.

Expression: \texttt{"DTM\_Filled@1"\ \textless{}=\ 0.8} (0.8m sea level
rise threshold)

\emph{{[}Screenshot placeholder - save as:
03\_flood\_calculation/raster\_calculator\_dialog.png{]}}

Result: Binary raster (1 = flooded, 0 = not flooded)

\emph{{[}Screenshot placeholder - save as:
03\_flood\_calculation/binary\_flood\_result.png{]}}

\subsection{Step 4.3: Define NoData
Value}\label{step-4.3-define-nodata-value}

Set non-flooded areas (value 0) to NoData using Translate tool.

\emph{{[}Screenshot placeholder - save as:
03\_flood\_calculation/translate\_dialog.png{]}}

Result: Cleaner visualization with only flooded areas visible.

\emph{{[}Screenshot placeholder - save as:
03\_flood\_calculation/nodata\_result.png{]}}

\subsection{Step 4.4: Polygonize}\label{step-4.4-polygonize}

Convert raster flood areas to vector polygons using Polygonize tool.

Enable ``Use 8-connectedness'' option.

\emph{{[}Screenshot placeholder - save as:
04\_vectorization/polygonize\_dialog.png{]}}

Result: Vector polygons representing flood extent.

\emph{{[}Screenshot placeholder - save as:
04\_vectorization/polygonized\_result.png{]}}

\subsection{Step 4.5: Filter Inland Water
Patches}\label{step-4.5-filter-inland-water-patches}

Remove inland areas not connected to coastline using Select by Location.

Select polygons that intersect with coastline layer.

\emph{{[}Screenshot placeholder - save as:
04\_vectorization/select\_by\_location\_dialog.png{]}}

Selected flood areas connected to coastline (highlighted in yellow).

\emph{{[}Screenshot placeholder - save as:
04\_vectorization/filtered\_selection.png{]}}

Save selected features as final flood extent layer.

\emph{{[}Screenshot placeholder - save as:
04\_vectorization/final\_flood\_extent.png{]}}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Display Step 4 screenshots}
\NormalTok{show\_step\_image(}\StringTok{"03\_flood\_calculation"}\NormalTok{, }\StringTok{"data\_loaded.png"}\NormalTok{,}
                \StringTok{"Step 4.1: DEM and coastline loaded in QGIS"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"03\_flood\_calculation"}\NormalTok{, }\StringTok{"raster\_calculator\_dialog.png"}\NormalTok{,}
                \StringTok{"Step 4.2: Raster Calculator with flood threshold expression"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"03\_flood\_calculation"}\NormalTok{, }\StringTok{"binary\_flood\_result.png"}\NormalTok{,}
                \StringTok{"Step 4.2: Binary flood raster result"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"03\_flood\_calculation"}\NormalTok{, }\StringTok{"translate\_dialog.png"}\NormalTok{,}
                \StringTok{"Step 4.3: Translate dialog setting NoData value"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"03\_flood\_calculation"}\NormalTok{, }\StringTok{"nodata\_result.png"}\NormalTok{,}
                \StringTok{"Step 4.3: Result with non{-}flooded areas as NoData"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"04\_vectorization"}\NormalTok{, }\StringTok{"polygonize\_dialog.png"}\NormalTok{,}
                \StringTok{"Step 4.4: Polygonize dialog"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"04\_vectorization"}\NormalTok{, }\StringTok{"polygonized\_result.png"}\NormalTok{,}
                \StringTok{"Step 4.4: Vector polygons created from flood raster"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"04\_vectorization"}\NormalTok{, }\StringTok{"select\_by\_location\_dialog.png"}\NormalTok{,}
                \StringTok{"Step 4.5: Select by Location dialog for coastline intersection"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"04\_vectorization"}\NormalTok{, }\StringTok{"filtered\_selection.png"}\NormalTok{,}
                \StringTok{"Step 4.5: Selected flood areas connected to coastline"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"04\_vectorization"}\NormalTok{, }\StringTok{"final\_flood\_extent.png"}\NormalTok{,}
                \StringTok{"Step 4.5: Final filtered flood extent"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

⚠️ \textbf{Image not found:} \texttt{data\_loaded.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}03\_flood\_calculation\textbackslash{}data\_loaded.png}

📋 \textbf{For:} Step 4.1: DEM and coastline loaded in QGIS

⚠️ \textbf{Image not found:} \texttt{raster\_calculator\_dialog.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}03\_flood\_calculation\textbackslash{}raster\_calculator\_dialog.png}

📋 \textbf{For:} Step 4.2: Raster Calculator with flood threshold
expression

⚠️ \textbf{Image not found:} \texttt{binary\_flood\_result.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}03\_flood\_calculation\textbackslash{}binary\_flood\_result.png}

📋 \textbf{For:} Step 4.2: Binary flood raster result

⚠️ \textbf{Image not found:} \texttt{translate\_dialog.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}03\_flood\_calculation\textbackslash{}translate\_dialog.png}

📋 \textbf{For:} Step 4.3: Translate dialog setting NoData value

⚠️ \textbf{Image not found:} \texttt{nodata\_result.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}03\_flood\_calculation\textbackslash{}nodata\_result.png}

📋 \textbf{For:} Step 4.3: Result with non-flooded areas as NoData

⚠️ \textbf{Image not found:} \texttt{polygonize\_dialog.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}04\_vectorization\textbackslash{}polygonize\_dialog.png}

📋 \textbf{For:} Step 4.4: Polygonize dialog

⚠️ \textbf{Image not found:} \texttt{polygonized\_result.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}04\_vectorization\textbackslash{}polygonized\_result.png}

📋 \textbf{For:} Step 4.4: Vector polygons created from flood raster

⚠️ \textbf{Image not found:} \texttt{select\_by\_location\_dialog.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}04\_vectorization\textbackslash{}select\_by\_location\_dialog.png}

📋 \textbf{For:} Step 4.5: Select by Location dialog for coastline
intersection

⚠️ \textbf{Image not found:} \texttt{filtered\_selection.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}04\_vectorization\textbackslash{}filtered\_selection.png}

📋 \textbf{For:} Step 4.5: Selected flood areas connected to coastline

⚠️ \textbf{Image not found:} \texttt{final\_flood\_extent.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}04\_vectorization\textbackslash{}final\_flood\_extent.png}

📋 \textbf{For:} Step 4.5: Final filtered flood extent

\subsection{Step 5: Create Final Map}\label{step-5-create-final-map}

Create comprehensive map layout with cartographic elements.

Organize layers: flood extent, DEM background, coastline reference.

\emph{{[}Screenshot placeholder - save as:
05\_quality\_control/layer\_styling.png{]}}

Set up print layout with map frame, legend, scale bar, north arrow.

\emph{{[}Screenshot placeholder - save as:
05\_quality\_control/print\_layout\_setup.png{]}}

Add title and cartographic elements.

\emph{{[}Screenshot placeholder - save as:
05\_quality\_control/map\_elements.png{]}}

Final map ready for export.

\emph{{[}Screenshot placeholder - save as:
06\_export/final\_map\_preview.png{]}}

Export options: PDF, PNG, print formats.

\emph{{[}Screenshot placeholder - save as:
06\_export/export\_options.png{]}}

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Display Step 5 screenshots}
\NormalTok{show\_step\_image(}\StringTok{"05\_quality\_control"}\NormalTok{, }\StringTok{"layer\_styling.png"}\NormalTok{,}
                \StringTok{"Step 5: Styling flood extent and background layers"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"05\_quality\_control"}\NormalTok{, }\StringTok{"print\_layout\_setup.png"}\NormalTok{,}
                \StringTok{"Step 5: Setting up print layout with map frame"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"05\_quality\_control"}\NormalTok{, }\StringTok{"map\_elements.png"}\NormalTok{,}
                \StringTok{"Step 5: Adding legend, scale bar, north arrow, and title"}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"06\_export"}\NormalTok{, }\StringTok{"final\_map\_preview.png"}\NormalTok{,}
                \StringTok{"Step 5: Final map layout ready for export"}\NormalTok{, width}\OperatorTok{=}\DecValTok{1000}\NormalTok{)}

\NormalTok{show\_step\_image(}\StringTok{"06\_export"}\NormalTok{, }\StringTok{"export\_options.png"}\NormalTok{,}
                \StringTok{"Step 5: Export options for different output formats"}\NormalTok{)}

\BuiltInTok{print}\NormalTok{(}\StringTok{"}\CharTok{\textbackslash{}n}\StringTok{"} \OperatorTok{+} \StringTok{"="}\OperatorTok{*}\DecValTok{60}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"🎯 UN{-}SPIDER METHODOLOGY COMPLETE"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"="}\OperatorTok{*}\DecValTok{60}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"✅ Step 1: Data acquisition and QGIS setup"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"✅ Step 2: DEM preprocessing"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"✅ Step 3: Shoreline preprocessing"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"✅ Step 4: Core flood processing (5 sub{-}steps)"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"✅ Step 5: Final map creation"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"="}\OperatorTok{*}\DecValTok{60}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"📋 DELIVERABLES:"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"   • Binary flood raster"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"   • Vector flood polygons"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"   • Filtered coastal flood extent"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"   • Final cartographic map"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"="}\OperatorTok{*}\DecValTok{60}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

⚠️ \textbf{Image not found:} \texttt{layer\_styling.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}05\_quality\_control\textbackslash{}layer\_styling.png}

📋 \textbf{For:} Step 5: Styling flood extent and background layers

⚠️ \textbf{Image not found:} \texttt{print\_layout\_setup.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}05\_quality\_control\textbackslash{}print\_layout\_setup.png}

📋 \textbf{For:} Step 5: Setting up print layout with map frame

⚠️ \textbf{Image not found:} \texttt{map\_elements.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}05\_quality\_control\textbackslash{}map\_elements.png}

📋 \textbf{For:} Step 5: Adding legend, scale bar, north arrow, and
title

⚠️ \textbf{Image not found:} \texttt{final\_map\_preview.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}06\_export\textbackslash{}final\_map\_preview.png}

📋 \textbf{For:} Step 5: Final map layout ready for export

⚠️ \textbf{Image not found:} \texttt{export\_options.png}

📝 \textbf{Save screenshot as:}
\texttt{c:\textbackslash{}AnacondaProjects\textbackslash{}04\_PREP\textbackslash{}Pictures\textbackslash{}QGIS\_workflow\textbackslash{}06\_export\textbackslash{}export\_options.png}

📋 \textbf{For:} Step 5: Export options for different output formats

\begin{verbatim}

============================================================
🎯 UN-SPIDER METHODOLOGY COMPLETE
============================================================
✅ Step 1: Data acquisition and QGIS setup
✅ Step 2: DEM preprocessing
✅ Step 3: Shoreline preprocessing
✅ Step 4: Core flood processing (5 sub-steps)
✅ Step 5: Final map creation
============================================================
📋 DELIVERABLES:
   • Binary flood raster
   • Vector flood polygons
   • Filtered coastal flood extent
   • Final cartographic map
============================================================
\end{verbatim}

\begin{center}\rule{0.5\linewidth}{0.5pt}\end{center}

\subsection{📚 Methodology Summary}\label{methodology-summary}

\subsubsection{UN-SPIDER Workflow
Implementation:}\label{un-spider-workflow-implementation}

This template follows the UN-SPIDER Step-by-Step Guide for Digital
Elevation Data Storm Surge Coastal Flood Modelling, adapted for Danish
sea level rise analysis.

\subsubsection{Processing Chain:}\label{processing-chain}

\begin{verbatim}
Data Acquisition → DEM Preprocessing → Shoreline Preprocessing →
Flood Processing (5 sub-steps) → Final Mapping
\end{verbatim}

\subsubsection{Key Adaptations:}\label{key-adaptations}

\begin{itemize}
\tightlist
\item
  \textbf{Higher Resolution:} Danish DTM vs.~Copernicus 30m DEM
\item
  \textbf{Local CRS:} ETRS89/UTM32N coordinate system
\item
  \textbf{Sea Level Rise:} 0.8m scenario for 2100 projections
\item
  \textbf{Hydrological Enhancement:} Sink filling for connectivity
\end{itemize}

\subsubsection{Deliverables:}\label{deliverables}

\begin{itemize}
\tightlist
\item
  Binary flood raster
\item
  Vector flood polygons
\item
  Filtered coastal flood extent
\item
  Cartographic map
\end{itemize}

\subsubsection{Alternative: QGIS Model
Approach}\label{alternative-qgis-model-approach}

For automated processing, the UN-SPIDER QGIS model is available at:
\href{https://github.com/josibregulla/SPEAR---Storm-Surge-Modelling}{https://github.com/josibregulla/SPEAR---Storm-Surge-Modelling}

\begin{center}\rule{0.5\linewidth}{0.5pt}\end{center}

\textbf{Template ready for documentation!} 📸

Fill in screenshots and observations as you work through each step.




\end{document}
