\ProvidesPackage{KUstyle}

\RequirePackage[margin=1in,top=1.5in,bottom=1.5in]{geometry}
\RequirePackage[utf8]{inputenc}
\RequirePackage[T1]{fontenc}
\RequirePackage{url}
\Urlmuskip=0mu plus 1mu
\RequirePackage{enumitem}
\setlist[itemize]{label=\textbullet}
\RequirePackage{fontspec}
\RequirePackage{eso-pic}
\RequirePackage{graphicx}
\RequirePackage{setspace}
\RequirePackage[absolute]{textpos}
\RequirePackage{hyperref}
\RequirePackage{xcolor}

\definecolor{KUrod}{RGB}{144,26,30} % Define KU red color

\hypersetup{
    colorlinks,
    citecolor=black,
    filecolor=black,
    linkcolor=black,
    urlcolor=black
}

\providecommand*{\advisor}[1]{\def\@advisor{#1}}
\providecommand*{\ptype}[1]{\def\@ptype{#1}}
\providecommand*{\subtitle}[1]{\def\@subtitle{#1}}
\providecommand*{\fpimage}[1]{\def\@fpimage{#1}}

\def\maketitle{
    \thispagestyle{empty}
    \AddToShipoutPictureBG*{\includegraphics[width=\paperwidth,height=\paperheight]{KU-logo.pdf}}
    \

    \ifdefined\@fpimage
        \begin{textblock}{16}(1.5,5.5)  % Increased width from 15 to 16 for more space
        \noindent\includegraphics*[width=1.05\textwidth, height=100mm, keepaspectratio]{\@fpimage}  % Slightly larger width and height
        \end{textblock}
    \fi

    \begin{textblock}{10}(1.5,12.2) \noindent\fontsize{16}{16}\selectfont \textbf{\@ptype}
    \end{textblock}

    \ifdefined\@subtitle
        \begin{textblock}{10}(1.5,12.6) \noindent\fontsize{14}{14}\selectfont \<AUTHOR>
    \fi

    \ifdefined\@ptype
        \begin{textblock}{10}(1.5,13.2)
        \noindent\fontsize{18}{18}\selectfont \textbf{\@title}
        \end{textblock}
    \fi

    \begin{textblock}{10}(1.5,13.7)
        \noindent\fontsize{14}{14}\selectfont \@subtitle
    \end{textblock}

    \begin{textblock}{10}(1.5,14.4)
    \noindent\fontsize{11}{11}\selectfont \@date
    \end{textblock}

    \ifdefined\@advisor
        \begin{textblock}{10}(1.5,14.8)
        \noindent\fontsize{11}{11}\selectfont \@advisor
        \end{textblock}
    \fi

    \newpage
    \noindent
}
