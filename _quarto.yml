# filepath: c:\AnacondaProjects\04_PREP\_quarto.yml.fixed
project:
  type: book
  output-dir: _book

book:
  title: 'PREP Rapport'
  author: '<PERSON><PERSON>'
  date: '2024'
  subtitle: 'A Subtitle for the Report'
  cover-image: 'Pictures/frontpage-image.jpg'
  chapters:
    - index.ipynb
    - Chapters/01_introduction.ipynb
    - Chapters/02_methods.ipynb
    - Chapters/03_results.ipynb
    - Chapters/04_discussion.ipynb
    - Chapters/05_conclusion.ipynb
#    - references.ipynb

bibliography: references.bib
# Removing CSL as we're using biblatex exclusively
# csl: apa.csl

# Set reference section title
reference-section-title: 'Bibliography'

format:
  html:
    theme:
      - cosmo
      - brand
    code-fold: true
    toc: true
    toc-depth: 3
    number-sections: true
    cover-image: 'KU-logo.pdf'
  pdf:
    documentclass: scrreprt
    pdf-engine: lualatex
    toc: true
    toc-depth: 3
    number-sections: true
    template: ku-template.tex
    include-in-header:
      text: |
        % Fontspec and KUstyle
        \usepackage{fontspec}
        \usepackage{KUstyle}
        % Set font search path for Windows and TinyTeX
        \defaultfontfeatures{Path=C:/jupyterwork/fonts/,Scale=MatchLowercase,Mapping=tex-text}
        % Optionally set OSFONTDIR for fontspec
        \IfFileExists{C:/jupyterwork/fonts/}{\edef\osfontdir{C:/jupyterwork/fonts/}}{}
    cite-method: biblatex
    biblatexoptions:
      - backend=biber
      - style=authoryear-ibid
      - sorting=nyt
      - maxbibnames=99
      - ibidtracker=true
      - ibidpage=true
      - block=none
    # Set environment variables for font cache and font search
    keep-tex: true
    pdf-engine-opt: '-shell-escape'
    # Set OSFONTDIR for fontspec
    env:
      OSFONTDIR: 'C:/jupyterwork/fonts;C:/Windows/fonts'
      TEXMFVAR: 'C:/jupyterwork/appdata/roaming/TinyTeX/texmf-var'
      TEXMFCACHE: 'C:/jupyterwork/appdata/roaming/TinyTeX/texmf-var/fonts/cache'
      TEXMFHOME: 'C:/jupyterwork/appdata/roaming/TinyTeX/texmf-home'
  docx:
    toc: true
    toc-depth: 3
    number-sections: true
    reference-doc: 'custom-reference-doc.docx'

execute:
  echo: true
  warning: false
