{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "jupyter: python3\n", "bibliography: ../references.bib\n", "biblatex: true\n", "# No need to specify biblatexoptions here as they are inherited from _quarto.yml\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Methods\n", "\n", "This study employs a comparative analysis of two hydrological modeling tools: QGIS and Scalgo Live, focusing on their application to coastal areas. QGIS was selected based on prior experience with the platform, while Scalgo Live was chosen following COWI's recommendation, as they utilized it for modeling in their report *\"Analyse af havstigninger i udvalgte Natura 2000-områder – Tab og indsatsmuligheder. Med udgangspunkt i Naturstyrelsens strandenge\"* [@ebbensgaardAnalyseAfHavstigninger2023]. Both tools are evaluated using a structured framework to determine their effectiveness for modeling sea level rise impacts on protected coastal habitats.\n", "\n", "## Evaluation Framework\n", "\n", "### Test Area Selection\n", "\n", "<PERSON><PERSON><PERSON><PERSON> was selected as the primary test area for this comparative analysis based on several key considerations:\n", "\n", "1.  Spatial analysis in QGIS identified this region as having a high concentration of protected coastal areas that would be potentially impacted by sea level rise\n", "2.  The area features extensive saltmarsh habitats that are particularly vulnerable to changes in sea level\n", "3.  Vejlerne contains significant § 3 protected nature areas and Natura 2000 designated sites, providing valuable conservation context (indsæt miljøportal og habitatplejeplan kilder)\n", "4.  **!!!** The site's representation in the COWI report allows for consistency in comparing methodological approaches\n", "\n", "### Evaluation Criteria\n", "\n", "The comparative analysis of QGIS and Scalgo Live is based on the following criteria:\n", "\n", "-   **Data integration:** Ability to incorporate and process various data types including digital elevation models (DEMs), land use data, hydrological data, and protected area designations\n", "-   **Sea level modeling accuracy:** Precision and reliability in representing projected sea level rise scenarios across different timeframes\n", "-   **Output quality:** Clarity, visual representation, and usability of the generated models for decision-making purposes\n", "-   **Analytical flexibility:** Capability to perform various hydrological analyses beyond basic flooding, including connectivity assessment and habitat impact evaluation\n", "-   **User accessibility:** Learning curve, technical requirements, and ease of use for practitioners with varying levels of GIS expertise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scenario Selection and Analysis Framework\n", "\n", "### Sea Level Rise Scenarios\n", "\n", "This analysis uses updated emission scenarios based on DMI's January 2025 recommendations, following the transition from RCP to SSP scenarios for sea level projections. Two scenarios represent the current planning range for Danish coastal climate adaptation.\n", "\n", "**SSP2-4.5** (moderate emissions) serves as the baseline scenario, with emissions declining from mid-century but not reaching climate neutrality by 2100 (2.7°C warming by 2081-2100). **SSP3-7.0** (high emissions) represents high robustness requirements as recommended by DMI for long-term coastal planning (3.6°C warming by 2081-2100). Current global climate policies suggest future emissions will fall within this range, making it highly relevant for practical applications.\n", "\n", "Using two emission scenarios rather than multiple return periods aligns with this study's primary objective: comparing analytical tools rather than conducting comprehensive risk assessment. This approach focuses on how different climate trajectories affect tool performance and output consistency.\n", "\n", "### Storm Event Selection\n", "\n", "Following COWI's 2023 methodology for Natura 2000 areas, this study focuses on 10-year return period storm events (T10). This choice balances analytical tractability with ecological relevance, as protected coastal habitats like saltmarshes are more vulnerable to repeated moderate disturbances than infrequent extreme events.\n", "\n", "T10 events correspond to storm surge heights that begin causing significant ecological impact in low-lying coastal areas. Climate projections indicate these events will become much more frequent under sea level rise, potentially occurring every 2-3 years by 2100, making T10 representative of future \"normal\" conditions.\n", "\n", "### Validation and Study Area\n", "\n", "To ensure methodological consistency, the analysis includes direct comparison with COWI's ScalgoLIVE results from Vejlerne. While COWI used RCP 8.5 scenarios, their water level projections (46 cm sea level rise by 2070, 103 cm by 2120) provide reference points for assessing platform consistency.\n", "\n", "Vejlerne was selected based on spatial analysis identifying high concentrations of vulnerable protected coastal areas, extensive saltmarsh habitats typical of Danish Natura 2000 sites, and continuity with existing professional analysis for validation.\n", "\n", "### Analysis Framework\n", "\n", "Given the tool comparison objective, this study prioritizes speed and comparability over comprehensive flood modeling. The framework produces outputs suitable for direct platform comparison while maintaining essential accuracy controls. For methodology evaluation, consistency between platforms is more critical than absolute precision, though both platforms are assessed against validation criteria to ensure meaningful results."]}], "metadata": {"kernelspec": {"display_name": "prep_book", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}