# Build outputs and temporary files
_book/
.ipynb_checkpoints/
*.log
.log
ku-template.fdb_latexmk

# Quarto build directory
/.quarto/

# Generated web assets
*.woff
*.js
*.min.js
*.umd.min.js
*.css

# LaTeX build files
*.aux
*.bbl
*.bcf
*.blg
*.fdb_latexmk
*.fls
*.run.xml
*.synctex.gz
*.toc
*.idx
*.ilg
*.ind
*.lof
*.lot
*.out
*.nav
*.snm
*.vrb

# Python cache and bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.conda/
conda-meta/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# R and RStudio (in case R is used alongside Python)
.Rhistory
.Rapp.history
.RData
.Ruserdata
.Rproj.user/
*.Rproj

# Data files (common extensions for academic projects)
*.csv
*.xlsx
*.xls
*.sav
*.dta
*.rds
*.feather
*.parquet
*.h5
*.hdf5
*.nc
*.cdf

# Image and media files that are often large or temporary
*.tiff
*.tif
*.bmp
*.raw
*.CR2
*.NEF
*.ARW
*.mov
*.mp4
*.avi
*.mkv
*.wmv
*.flv

# Backup files
*.bak
*.backup
*~
*.swp
*.swo
*#
.#*

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Keep biblatex documentation (useful reference)
# biblatex.pdf

# Keep important project-specific documentation and templates
!README.md
!*.md
!custom-reference-doc.docx
!ku-template.tex
!KUstyle.sty
!brand.scss
Microsoft/Windows/PowerShell/ModuleAnalysisCache

*.png
