{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "jupyter: python3\n", "bibliography: ../references.bib\n", "biblatex: true\n", "# No need to specify biblatexoptions here as they are inherited from _quarto.yml\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Results\n", "\n", "Describe main results here.\n", "\n", "- Tal om resultaterne (kortene) fra de to forskellige programmer\n", "- Husk at skrive ned, hvad du gør i løbet af analysen\n", "- Vælg **TO** scenarier (begynd med ét) - vigtigt at tale om deres evne til at forudsige (kan programmet leve op til det?)\n", "- Skriv at du kun arbejder med AVJNF fordi adgang til data er nemmere\n", "- Det bliver til en Case-study fordi <PERSON><PERSON> har et område som passer til mit speciale\n", "- Hvorfor bruge Scalgo i stedet for GDAL og Saga i QGIS"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "prep_book", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}