---
jupyter: python3
bibliography: ../../references.bib
link-citations: true
title: QGIS Flood Analysis Documentation
---



---

::: {.content-hidden when-format="html"}
## 📸 Screenshot Guidelines

### Directory Structure:
```
Pictures/QGIS_workflow/
├── 01_data_loading/
├── 02_preprocessing/
├── 03_flood_calculation/
├── 04_vectorization/
├── 05_quality_control/
└── 06_export/
```

### Image Naming:
- Use descriptive names: `fill_sinks_dialog.png`
- Include step context: `raster_calculator_dialog.png`
- Show results: `binary_flood_result.png`

### Quality Standards:
- High resolution (1920x1080 minimum)
- PNG format for UI screenshots
- Include relevant toolbars and panels
- Highlight important elements with arrows/circles

:::

<!-- PROOFREAD PROMPT: Fix only grammar/spelling errors. Keep my exact writing style. No rewrites. -->


```{python}
#| echo: false
#| include: false
#| output: false

# =============================================================================
# SETUP CELL - Run this first to initialize image handling system
# =============================================================================
# 📁 Creates organized directory structure for QGIS workflow images
# 🖼️ Defines helper function for consistent image display
# ⚠️  IMPORTANT: Keep this cell - needed for notebook functionality!

# Image handling setup for documentation
import os
from pathlib import Path
from IPython.display import Image, display, Markdown
import base64

# Setup image directory structure
project_root = Path(r"c:\AnacondaProjects\04_PREP")
images_dir = project_root / "Pictures" / "QGIS_workflow"
images_dir.mkdir(exist_ok=True)

# Create subdirectories for organized image storage
step_dirs = [
    "01_data_loading",
    "02_preprocessing",
    "03_flood_calculation",
    "04_vectorization",
    "05_quality_control",
    "06_export"
]

for step_dir in step_dirs:
    (images_dir / step_dir).mkdir(exist_ok=True)

print("📁 Image Directory Structure:")
print(f"   Base: {images_dir}")
for step_dir in step_dirs:
    print(f"   └── {step_dir}/")

# Helper function to display images with consistent formatting
def show_step_image(step_folder, image_name, caption="", width=800):
    """
    Display an image with caption for workflow documentation

    Args:
        step_folder: Name of the step folder (e.g., "01_data_loading")
        image_name: Name of the image file
        caption: Optional caption for the image
        width: Width in pixels for display
    """
    image_path = images_dir / step_folder / image_name

    if image_path.exists():
        display(Markdown(f"**{caption}**" if caption else ""))
        display(Image(filename=str(image_path), width=width))
        display(Markdown(f"*Image: {image_path.relative_to(project_root)}*"))
    else:
        display(Markdown(f"⚠️ **Image not found:** `{image_name}`"))
        display(Markdown(f"📝 **Save screenshot as:** `{image_path}`"))
        if caption:
            display(Markdown(f"📋 **For:** {caption}"))

print("\n✅ Setup complete! Image handling system ready.")
print("💡 You can now take screenshots and use show_step_image() to display them.")
```

## Following UN-SPIDER Storm Surge Coastal Flood Modelling Methodology

**Project:** Vejlerne Sea Level Rise Analysis
**Methodology:** UN-SPIDER Step-by-Step Guide adapted for Danish coastal conditions

## Requirements

### Computer Requirements:
- QGIS 3.28 or newer
- Processing power for raster analysis
- Storage space for DEM and output files

### Required Datasets:

1. **Digital Elevation Model (DEM)** - Danish DTM from Dataforsyningen.dk [@kli2025danmarkshojdemodel]

   The DHM/Terrain model is derived from the national point cloud which forms the basis for the Danish Height Model (DHM). To create the terrain model, the point cloud is filtered to include only points classified as 'terrain', while other classifications such as buildings, vegetation, and other objects are removed, resulting in the final DTM model that has a 0.4 meter spatial resolution with vertical accuracy of ±0.05-0.10m (DVR90 datum).

2. **Coastline vector layer** - 'Kystinddeling' from DMI:

   In order to clip the DEM along the coastline I've used the layer 'Kystinddeling' which is a part of the data from the climate variable 'Vandstand og stormflod fordelt på Kyst' from DMI's 'Klimaatlas'. The 'Kystinddeling's layer also comes as a part of the main dataset 'VandstandStormflodKyst' containing the data for the sea level rise scenarios used to define the extent/flooding for the DEM and described below.

3. **VandstandStormflodKyst** - Data on water levels and storm surges from DMI:

   This dataset contains climate projections for coastal water levels based on DMI's Climate Atlas. The data includes mean sea level rise (MHVS) and storm surge return periods (T10, T20, T100) for different emission scenarios. Data varies by coastal location. The dataset enables analysis of sea level rise and storm events for coastal flood modeling in Danish areas.


### Key Settings:
- **Coordinate System:** EPSG:25832 (ETRS89 / UTM zone 32N)
- **Flood Scenarios:** SSP2-4,5 T10 (2070) and SSP3-7,0 T10 (2100)
- **DEM Type:** DTM (Digital Terrain Model) - Danish 0,4m resolution

## Step 1: Data Acquisition and QGIS Setup

Open QGIS and create new project with EPSG:25832 coordinate system.

Load Danish DTM data from Datafordeleren.

*[Screenshot placeholder - save as: 01_data_loading/project_setup.png]*


```{python}
# Load data
show_step_image("01_data_loading", "DTM_load.png",
                "Retrieval of DTM as a WCS from Datafordeleren")
```

## Step 2: DEM Preprocessing

Fill sinks in the DEM to ensure hydrological connectivity for flood modeling.

Use SAGA → Terrain Analysis - Hydrology → Fill Sinks (Wang & Liu).

*[Screenshot placeholder - save as: 02_preprocessing/fill_sinks_dialog.png]*

Result: DTM_Filled.tif with improved connectivity for coastal flood analysis.

*[Screenshot placeholder - save as: 02_preprocessing/filled_dem_result.png]*

```{python}
# Display Step 2 screenshots
show_step_image("02_preprocessing", "fill_sinks_dialog.png",
                "Fill Sinks algorithm dialog with parameters")

show_step_image("02_preprocessing", "filled_dem_result.png",
                "Comparison of original vs filled DEM")
```

## Step 3: Shoreline Preprocessing

Load or create coastline data for the study area.

Clip global coastline data to study area extent using 'Clip Vector by Extent'.

*[Screenshot placeholder - save as: 02_preprocessing/clip_by_extent.png]*

Dissolve coastline segments into unified layer using 'Dissolve' tool.

*[Screenshot placeholder - save as: 02_preprocessing/dissolve_coastline.png]*

Final preprocessed coastline ready for flood connectivity analysis.

*[Screenshot placeholder - save as: 02_preprocessing/final_coastline.png]*

```{python}
# Display Step 3 screenshots
show_step_image("02_preprocessing", "coastline_loading.png",
                "Loading coastline/water polygon data")

show_step_image("02_preprocessing", "clip_by_extent.png",
                "Clipping coastline to study area extent")

show_step_image("02_preprocessing", "dissolve_coastline.png",
                "Dissolving coastline segments into unified layer")

show_step_image("02_preprocessing", "final_coastline.png",
                "Final preprocessed coastline ready for analysis")
```

## Step 4.1: Verify Data Loading

Ensure both DEM and preprocessed coastline are loaded in QGIS project.

*[Screenshot placeholder - save as: 03_flood_calculation/data_loaded.png]*

## Step 4.2: Raster Calculation

Create binary flood mask using Raster Calculator.

Expression: `"DTM_Filled@1" <= 0.8` (0.8m sea level rise threshold)

*[Screenshot placeholder - save as: 03_flood_calculation/raster_calculator_dialog.png]*

Result: Binary raster (1 = flooded, 0 = not flooded)

*[Screenshot placeholder - save as: 03_flood_calculation/binary_flood_result.png]*

## Step 4.3: Define NoData Value

Set non-flooded areas (value 0) to NoData using Translate tool.

*[Screenshot placeholder - save as: 03_flood_calculation/translate_dialog.png]*

Result: Cleaner visualization with only flooded areas visible.

*[Screenshot placeholder - save as: 03_flood_calculation/nodata_result.png]*

## Step 4.4: Polygonize

Convert raster flood areas to vector polygons using Polygonize tool.

Enable "Use 8-connectedness" option.

*[Screenshot placeholder - save as: 04_vectorization/polygonize_dialog.png]*

Result: Vector polygons representing flood extent.

*[Screenshot placeholder - save as: 04_vectorization/polygonized_result.png]*

## Step 4.5: Filter Inland Water Patches

Remove inland areas not connected to coastline using Select by Location.

Select polygons that intersect with coastline layer.

*[Screenshot placeholder - save as: 04_vectorization/select_by_location_dialog.png]*

Selected flood areas connected to coastline (highlighted in yellow).

*[Screenshot placeholder - save as: 04_vectorization/filtered_selection.png]*

Save selected features as final flood extent layer.

*[Screenshot placeholder - save as: 04_vectorization/final_flood_extent.png]*

```{python}
# Display Step 4 screenshots
show_step_image("03_flood_calculation", "data_loaded.png",
                "Step 4.1: DEM and coastline loaded in QGIS")

show_step_image("03_flood_calculation", "raster_calculator_dialog.png",
                "Step 4.2: Raster Calculator with flood threshold expression")

show_step_image("03_flood_calculation", "binary_flood_result.png",
                "Step 4.2: Binary flood raster result")

show_step_image("03_flood_calculation", "translate_dialog.png",
                "Step 4.3: Translate dialog setting NoData value")

show_step_image("03_flood_calculation", "nodata_result.png",
                "Step 4.3: Result with non-flooded areas as NoData")

show_step_image("04_vectorization", "polygonize_dialog.png",
                "Step 4.4: Polygonize dialog")

show_step_image("04_vectorization", "polygonized_result.png",
                "Step 4.4: Vector polygons created from flood raster")

show_step_image("04_vectorization", "select_by_location_dialog.png",
                "Step 4.5: Select by Location dialog for coastline intersection")

show_step_image("04_vectorization", "filtered_selection.png",
                "Step 4.5: Selected flood areas connected to coastline")

show_step_image("04_vectorization", "final_flood_extent.png",
                "Step 4.5: Final filtered flood extent")
```

## Step 5: Create Final Map

Create comprehensive map layout with cartographic elements.

Organize layers: flood extent, DEM background, coastline reference.

*[Screenshot placeholder - save as: 05_quality_control/layer_styling.png]*

Set up print layout with map frame, legend, scale bar, north arrow.

*[Screenshot placeholder - save as: 05_quality_control/print_layout_setup.png]*

Add title and cartographic elements.

*[Screenshot placeholder - save as: 05_quality_control/map_elements.png]*

Final map ready for export.

*[Screenshot placeholder - save as: 06_export/final_map_preview.png]*

Export options: PDF, PNG, print formats.

*[Screenshot placeholder - save as: 06_export/export_options.png]*

```{python}
# Display Step 5 screenshots
show_step_image("05_quality_control", "layer_styling.png",
                "Step 5: Styling flood extent and background layers")

show_step_image("05_quality_control", "print_layout_setup.png",
                "Step 5: Setting up print layout with map frame")

show_step_image("05_quality_control", "map_elements.png",
                "Step 5: Adding legend, scale bar, north arrow, and title")

show_step_image("06_export", "final_map_preview.png",
                "Step 5: Final map layout ready for export", width=1000)

show_step_image("06_export", "export_options.png",
                "Step 5: Export options for different output formats")

print("\n" + "="*60)
print("🎯 UN-SPIDER METHODOLOGY COMPLETE")
print("="*60)
print("✅ Step 1: Data acquisition and QGIS setup")
print("✅ Step 2: DEM preprocessing")
print("✅ Step 3: Shoreline preprocessing")
print("✅ Step 4: Core flood processing (5 sub-steps)")
print("✅ Step 5: Final map creation")
print("="*60)
print("📋 DELIVERABLES:")
print("   • Binary flood raster")
print("   • Vector flood polygons")
print("   • Filtered coastal flood extent")
print("   • Final cartographic map")
print("="*60)
```

---

## 📚 Methodology Summary

### UN-SPIDER Workflow Implementation:
This template follows the UN-SPIDER Step-by-Step Guide for Digital Elevation Data Storm Surge Coastal Flood Modelling, adapted for Danish sea level rise analysis.

### Processing Chain:
```
Data Acquisition → DEM Preprocessing → Shoreline Preprocessing →
Flood Processing (5 sub-steps) → Final Mapping
```

### Key Adaptations:
- **Higher Resolution:** Danish DTM vs. Copernicus 30m DEM
- **Local CRS:** ETRS89/UTM32N coordinate system
- **Sea Level Rise:** 0.8m scenario for 2100 projections
- **Hydrological Enhancement:** Sink filling for connectivity

### Deliverables:
- Binary flood raster
- Vector flood polygons
- Filtered coastal flood extent
- Cartographic map

### Alternative: QGIS Model Approach
For automated processing, the UN-SPIDER QGIS model is available at:
[https://github.com/josibregulla/SPEAR---Storm-Surge-Modelling](https://github.com/josibregulla/SPEAR---Storm-Surge-Modelling)

---

**Template ready for documentation!** 📸

Fill in screenshots and observations as you work through each step.

