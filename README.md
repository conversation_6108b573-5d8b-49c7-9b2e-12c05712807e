# PREP Rapport Quarto Book Project

This repository contains a Quarto Book project for the PREP Rapport, styled according to the UCPH thesis template. The book is built using Jupyter Notebook files instead of .qmd files.

## Project Structure

- `_quarto.yml`: Configuration file for the Quarto book
- `index.ipynb`: Preface and introduction to the book
- `Chapters/`: Directory containing the chapter notebooks
  - `introduction.ipynb`: Introduction chapter
  - `methods.ipynb`: Methods chapter
  - `results.ipynb`: Results chapter
  - `discussion.ipynb`: Discussion chapter
  - `conclusion.ipynb`: Conclusion chapter
- `Figures/`: Directory for storing figures
- `Pictures/`: Directory for storing pictures and images
- `references.bib`: Bibliography file in BibTeX format
- `references.ipynb`: References page
- `brand.scss`: Custom styling for HTML output
- `custom-reference-doc.docx`: Custom Word template for docx output

## Getting Started

### Prerequisites

- [Quarto](https://quarto.org/docs/get-started/) (version 1.3 or higher)
- [Jupyter Notebook](https://jupyter.org/install)
- Python 3.x with the following packages:
  - matplotlib
  - numpy
  - pandas

### Rendering the Book

To render the book to all formats (HTML, PDF, and Word):

```bash
quarto render
```

To render to a specific format:

```bash
quarto render --to html
quarto render --to pdf
quarto render --to docx
```

### Preview the Book

To preview the book in HTML format with live updates as you edit:

```bash
quarto preview
```

## Customizing the Book

### Adding a New Chapter

1. Create a new Jupyter Notebook file in the `Chapters/` directory
2. Add YAML front matter at the beginning of the notebook in a raw cell:

   ```yaml
   ---
   title: "Chapter Title"
   jupyter: python3
   ---
   ```

3. Add the chapter to the `_quarto.yml` file under the `chapters:` section

### Adding Figures

1. Place figure files in the `Figures/` directory
2. Reference figures in your notebooks using Markdown syntax:

   ```markdown
   ![Caption](../Figures/figure-name.png)
   ```

3. For cross-references, use code cells with the `#| label:` and `#| fig-cap:` options:

   ```python
   #| label: fig-example
   #| fig-cap: "Example figure caption"

   # Your code to generate the figure
   ```

4. Reference the figure in text using `@fig-example`

### Adding Citations

1. Add references to the `references.bib` file in BibTeX format
2. Cite references in your notebooks using `@citation-key`

## Styling

- HTML output is styled using the `brand.scss` file
- Word output is styled using the `custom-reference-doc.docx` template
- PDF output uses the LaTeX settings in the `_quarto.yml` file

## License

This project is licensed under the MIT License - see the LICENSE file for details.
