/*-- scss:defaults --*/
// UCPH colors
$ku-red: #901A1E;
$ku-dark-grey: #252525;
$ku-light-grey: #EFEFEF;

// Base document colors
$body-bg: white;
$body-color: $ku-dark-grey;
$link-color: $ku-red;
$headings-color: $ku-red;

// Navigation
$navbar-bg: $ku-red;
$navbar-fg: white;
$navbar-hl: $ku-light-grey;

// Code blocks
$code-block-bg: $ku-light-grey;

/*-- scss:rules --*/
// Custom title page
.quarto-title-block .quarto-title-banner {
  background-color: $ku-red;
  color: white;
  padding: 2em;
  margin-bottom: 2em;
}

// Headings
h1, h2, h3, h4, h5, h6 {
  font-family: Arial, sans-serif;
  font-weight: bold;
}

h1 {
  border-bottom: 2px solid $ku-red;
  padding-bottom: 0.5em;
}

// Body text
body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

// Table of contents
.sidebar-title {
  color: $ku-red;
}

// Code blocks
pre {
  border-left: 5px solid $ku-red;
}

// Figures and tables
figure {
  margin: 2em 0;
}

figcaption {
  font-style: italic;
  color: $ku-dark-grey;
  margin-top: 0.5em;
}

table {
  margin: 2em 0;
  border-collapse: collapse;
}

th {
  background-color: $ku-red;
  color: white;
}

// Footer
.footer {
  border-top: 1px solid $ku-light-grey;
  margin-top: 2em;
  padding-top: 1em;
}
