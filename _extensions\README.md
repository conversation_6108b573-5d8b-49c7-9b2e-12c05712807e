# Quarto Extensions Bootstrap

This directory was created to bootstrap the Quarto extension system.

## Why This Matters

The Quarto VS Code extension depends on the Quarto CLI's extension discovery mechanism, which looks for `_extensions` directories in the project. Without this directory, the extension context may not initialize properly, causing VS Code commands like 'quarto.preview' to not be registered.

## How It Works

1. Quarto CLI scans for `_extensions` directories starting from the current file's directory
2. It recursively searches up the directory tree until it finds a project root
3. The extension context is initialized based on discovered extensions
4. The VS Code extension uses this context to register commands

## Next Steps

You can now:

1. Install Quarto extensions using `quarto add <extension-name>`
2. Extensions will be installed in this directory
3. The VS Code extension should now properly register commands

## Common Extensions to Consider

- `quarto add quarto-ext/lightbox` - Image lightboxes
- `quarto add quarto-ext/attribution` - Attribution blocks
- `quarto add quarto-journals/jss` - Journal formats
