<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.7.32">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>QGIS Flood Analysis Documentation</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
html { -webkit-text-size-adjust: 100%; }
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
/* CSS for citations */
div.csl-bib-body { }
div.csl-entry {
  clear: both;
  margin-bottom: 0em;
}
.hanging-indent div.csl-entry {
  margin-left:2em;
  text-indent:-2em;
}
div.csl-left-margin {
  min-width:2em;
  float:left;
}
div.csl-right-inline {
  margin-left:2em;
  padding-left:1em;
}
div.csl-indent {
  margin-left: 2em;
}</style>


<script src="QGIS_flood_analysis_files/libs/clipboard/clipboard.min.js"></script>
<script src="QGIS_flood_analysis_files/libs/quarto-html/quarto.js" type="module"></script>
<script src="QGIS_flood_analysis_files/libs/quarto-html/tabsets/tabsets.js" type="module"></script>
<script src="QGIS_flood_analysis_files/libs/quarto-html/popper.min.js"></script>
<script src="QGIS_flood_analysis_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="QGIS_flood_analysis_files/libs/quarto-html/anchor.min.js"></script>
<link href="QGIS_flood_analysis_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="QGIS_flood_analysis_files/libs/quarto-html/quarto-syntax-highlighting-37eea08aefeeee20ff55810ff984fec1.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="QGIS_flood_analysis_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="QGIS_flood_analysis_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="QGIS_flood_analysis_files/libs/bootstrap/bootstrap-bb462d781dde1847d9e3ccf7736099dd.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent quarto-light">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<h1 class="title">QGIS Flood Analysis Documentation</h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<hr>
<!-- PROOFREAD PROMPT: Fix only grammar/spelling errors. Keep my exact writing style. No rewrites. -->
<section id="following-un-spider-storm-surge-coastal-flood-modelling-methodology" class="level2">
<h2 class="anchored" data-anchor-id="following-un-spider-storm-surge-coastal-flood-modelling-methodology">Following UN-SPIDER Storm Surge Coastal Flood Modelling Methodology</h2>
<p><strong>Project:</strong> Vejlerne Sea Level Rise Analysis <strong>Methodology:</strong> UN-SPIDER Step-by-Step Guide adapted for Danish coastal conditions</p>
</section>
<section id="requirements" class="level2">
<h2 class="anchored" data-anchor-id="requirements">Requirements</h2>
<section id="computer-requirements" class="level3">
<h3 class="anchored" data-anchor-id="computer-requirements">Computer Requirements:</h3>
<ul>
<li>QGIS 3.28 or newer</li>
<li>Processing power for raster analysis</li>
<li>Storage space for DEM and output files</li>
</ul>
</section>
<section id="required-datasets" class="level3">
<h3 class="anchored" data-anchor-id="required-datasets">Required Datasets:</h3>
<ol type="1">
<li><p><strong>Digital Elevation Model (DEM)</strong> - Danish DTM from Dataforsyningen.dk <span class="citation" data-cites="kli2025danmarkshojdemodel">(<a href="#ref-kli2025danmarkshojdemodel" role="doc-biblioref">Klimadatastyrelsen 2025</a>)</span></p>
<p>The DHM/Terrain model is derived from the national point cloud which forms the basis for the Danish Height Model (DHM). To create the terrain model, the point cloud is filtered to include only points classified as ‘terrain’, while other classifications such as buildings, vegetation, and other objects are removed, resulting in the final DTM model that has a 0.4 meter spatial resolution with vertical accuracy of ±0.05-0.10m (DVR90 datum).</p></li>
<li><p><strong>Coastline vector layer</strong> - ‘Kystinddeling’ from DMI:</p>
<p>In order to clip the DEM along the coastline I’ve used the layer ‘Kystinddeling’ which is a part of the data from the climate variable ‘Vandstand og stormflod fordelt på Kyst’ from DMI’s ‘Klimaatlas’. The ‘Kystinddeling’s layer also comes as a part of the main dataset ’VandstandStormflodKyst’ containing the data for the sea level rise scenarios used to define the extent/flooding for the DEM and described below.</p></li>
<li><p><strong>VandstandStormflodKyst</strong> - Data on water levels and storm surges from DMI:</p>
<p>This dataset contains climate projections for coastal water levels based on DMI’s Climate Atlas. The data includes mean sea level rise (MHVS) and storm surge return periods (T10, T20, T100) for different emission scenarios. Data varies by coastal location. The dataset enables analysis of sea level rise and storm events for coastal flood modeling in Danish areas.</p></li>
</ol>
</section>
<section id="key-settings" class="level3">
<h3 class="anchored" data-anchor-id="key-settings">Key Settings:</h3>
<ul>
<li><strong>Coordinate System:</strong> EPSG:25832 (ETRS89 / UTM zone 32N)</li>
<li><strong>Flood Scenarios:</strong> SSP2-4,5 T10 (2070) and SSP3-7,0 T10 (2100)</li>
<li><strong>DEM Type:</strong> DTM (Digital Terrain Model) - Danish 0,4m resolution</li>
</ul>
</section>
</section>
<section id="step-1-data-acquisition-and-qgis-setup" class="level2">
<h2 class="anchored" data-anchor-id="step-1-data-acquisition-and-qgis-setup">Step 1: Data Acquisition and QGIS Setup</h2>
<p>Open QGIS and create new project with EPSG:25832 coordinate system.</p>
<p>Load Danish DTM data from Datafordeleren.</p>
<p><em>[Screenshot placeholder - save as: 01_data_loading/project_setup.png]</em></p>
<div id="f8339805" class="cell" data-execution_count="2">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load data</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"01_data_loading"</span>, <span class="st">"DTM_load.png"</span>,</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Retrieval of DTM as a WCS from Datafordeleren"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display cell-output-markdown">
<p><strong>Retrieval of DTM as a WCS from Datafordeleren</strong></p>
</div>
<div class="cell-output cell-output-display">
<div>
<figure class="figure">
<p><img src="QGIS_flood_analysis_files/figure-html/cell-3-output-2.png" class="img-fluid figure-img" width="800"></p>
</figure>
</div>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>*Image: Pictures_workflow\01_data_loading_load.png*</p>
</div>
</div>
</section>
<section id="step-2-dem-preprocessing" class="level2">
<h2 class="anchored" data-anchor-id="step-2-dem-preprocessing">Step 2: DEM Preprocessing</h2>
<p>Fill sinks in the DEM to ensure hydrological connectivity for flood modeling.</p>
<p>Use SAGA → Terrain Analysis - Hydrology → Fill Sinks (Wang &amp; Liu).</p>
<p><em>[Screenshot placeholder - save as: 02_preprocessing/fill_sinks_dialog.png]</em></p>
<p>Result: DTM_Filled.tif with improved connectivity for coastal flood analysis.</p>
<p><em>[Screenshot placeholder - save as: 02_preprocessing/filled_dem_result.png]</em></p>
<div id="acc1adee" class="cell" data-execution_count="3">
<div class="sourceCode cell-code" id="cb2"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Display Step 2 screenshots</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"fill_sinks_dialog.png"</span>,</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Fill Sinks algorithm dialog with parameters"</span>)</span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"filled_dem_result.png"</span>,</span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Comparison of original vs filled DEM"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>fill_sinks_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\fill_sinks_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Fill Sinks algorithm dialog with parameters</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>filled_dem_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\filled_dem_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Comparison of original vs filled DEM</p>
</div>
</div>
</section>
<section id="step-3-shoreline-preprocessing" class="level2">
<h2 class="anchored" data-anchor-id="step-3-shoreline-preprocessing">Step 3: Shoreline Preprocessing</h2>
<p>Load or create coastline data for the study area.</p>
<p>Clip global coastline data to study area extent using ‘Clip Vector by Extent’.</p>
<p><em>[Screenshot placeholder - save as: 02_preprocessing/clip_by_extent.png]</em></p>
<p>Dissolve coastline segments into unified layer using ‘Dissolve’ tool.</p>
<p><em>[Screenshot placeholder - save as: 02_preprocessing/dissolve_coastline.png]</em></p>
<p>Final preprocessed coastline ready for flood connectivity analysis.</p>
<p><em>[Screenshot placeholder - save as: 02_preprocessing/final_coastline.png]</em></p>
<div id="0df9e302" class="cell" data-execution_count="4">
<div class="sourceCode cell-code" id="cb3"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Display Step 3 screenshots</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"coastline_loading.png"</span>,</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Loading coastline/water polygon data"</span>)</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"clip_by_extent.png"</span>,</span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Clipping coastline to study area extent"</span>)</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"dissolve_coastline.png"</span>,</span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Dissolving coastline segments into unified layer"</span>)</span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"02_preprocessing"</span>, <span class="st">"final_coastline.png"</span>,</span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Final preprocessed coastline ready for analysis"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>coastline_loading.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\coastline_loading.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Loading coastline/water polygon data</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>clip_by_extent.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\clip_by_extent.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Clipping coastline to study area extent</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>dissolve_coastline.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\dissolve_coastline.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Dissolving coastline segments into unified layer</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>final_coastline.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\02_preprocessing\final_coastline.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Final preprocessed coastline ready for analysis</p>
</div>
</div>
</section>
<section id="step-4.1-verify-data-loading" class="level2">
<h2 class="anchored" data-anchor-id="step-4.1-verify-data-loading">Step 4.1: Verify Data Loading</h2>
<p>Ensure both DEM and preprocessed coastline are loaded in QGIS project.</p>
<p><em>[Screenshot placeholder - save as: 03_flood_calculation/data_loaded.png]</em></p>
</section>
<section id="step-4.2-raster-calculation" class="level2">
<h2 class="anchored" data-anchor-id="step-4.2-raster-calculation">Step 4.2: Raster Calculation</h2>
<p>Create binary flood mask using Raster Calculator.</p>
<p>Expression: <code>"DTM_Filled@1" &lt;= 0.8</code> (0.8m sea level rise threshold)</p>
<p><em>[Screenshot placeholder - save as: 03_flood_calculation/raster_calculator_dialog.png]</em></p>
<p>Result: Binary raster (1 = flooded, 0 = not flooded)</p>
<p><em>[Screenshot placeholder - save as: 03_flood_calculation/binary_flood_result.png]</em></p>
</section>
<section id="step-4.3-define-nodata-value" class="level2">
<h2 class="anchored" data-anchor-id="step-4.3-define-nodata-value">Step 4.3: Define NoData Value</h2>
<p>Set non-flooded areas (value 0) to NoData using Translate tool.</p>
<p><em>[Screenshot placeholder - save as: 03_flood_calculation/translate_dialog.png]</em></p>
<p>Result: Cleaner visualization with only flooded areas visible.</p>
<p><em>[Screenshot placeholder - save as: 03_flood_calculation/nodata_result.png]</em></p>
</section>
<section id="step-4.4-polygonize" class="level2">
<h2 class="anchored" data-anchor-id="step-4.4-polygonize">Step 4.4: Polygonize</h2>
<p>Convert raster flood areas to vector polygons using Polygonize tool.</p>
<p>Enable “Use 8-connectedness” option.</p>
<p><em>[Screenshot placeholder - save as: 04_vectorization/polygonize_dialog.png]</em></p>
<p>Result: Vector polygons representing flood extent.</p>
<p><em>[Screenshot placeholder - save as: 04_vectorization/polygonized_result.png]</em></p>
</section>
<section id="step-4.5-filter-inland-water-patches" class="level2">
<h2 class="anchored" data-anchor-id="step-4.5-filter-inland-water-patches">Step 4.5: Filter Inland Water Patches</h2>
<p>Remove inland areas not connected to coastline using Select by Location.</p>
<p>Select polygons that intersect with coastline layer.</p>
<p><em>[Screenshot placeholder - save as: 04_vectorization/select_by_location_dialog.png]</em></p>
<p>Selected flood areas connected to coastline (highlighted in yellow).</p>
<p><em>[Screenshot placeholder - save as: 04_vectorization/filtered_selection.png]</em></p>
<p>Save selected features as final flood extent layer.</p>
<p><em>[Screenshot placeholder - save as: 04_vectorization/final_flood_extent.png]</em></p>
<div id="cada53f0" class="cell" data-execution_count="5">
<div class="sourceCode cell-code" id="cb4"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Display Step 4 screenshots</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"03_flood_calculation"</span>, <span class="st">"data_loaded.png"</span>,</span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.1: DEM and coastline loaded in QGIS"</span>)</span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"03_flood_calculation"</span>, <span class="st">"raster_calculator_dialog.png"</span>,</span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.2: Raster Calculator with flood threshold expression"</span>)</span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"03_flood_calculation"</span>, <span class="st">"binary_flood_result.png"</span>,</span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.2: Binary flood raster result"</span>)</span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"03_flood_calculation"</span>, <span class="st">"translate_dialog.png"</span>,</span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.3: Translate dialog setting NoData value"</span>)</span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"03_flood_calculation"</span>, <span class="st">"nodata_result.png"</span>,</span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.3: Result with non-flooded areas as NoData"</span>)</span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"04_vectorization"</span>, <span class="st">"polygonize_dialog.png"</span>,</span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.4: Polygonize dialog"</span>)</span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"04_vectorization"</span>, <span class="st">"polygonized_result.png"</span>,</span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.4: Vector polygons created from flood raster"</span>)</span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"04_vectorization"</span>, <span class="st">"select_by_location_dialog.png"</span>,</span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.5: Select by Location dialog for coastline intersection"</span>)</span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"04_vectorization"</span>, <span class="st">"filtered_selection.png"</span>,</span>
<span id="cb4-27"><a href="#cb4-27" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.5: Selected flood areas connected to coastline"</span>)</span>
<span id="cb4-28"><a href="#cb4-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-29"><a href="#cb4-29" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"04_vectorization"</span>, <span class="st">"final_flood_extent.png"</span>,</span>
<span id="cb4-30"><a href="#cb4-30" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 4.5: Final filtered flood extent"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>data_loaded.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\03_flood_calculation\data_loaded.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.1: DEM and coastline loaded in QGIS</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>raster_calculator_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\03_flood_calculation\raster_calculator_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.2: Raster Calculator with flood threshold expression</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>binary_flood_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\03_flood_calculation\binary_flood_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.2: Binary flood raster result</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>translate_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\03_flood_calculation\translate_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.3: Translate dialog setting NoData value</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>nodata_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\03_flood_calculation\nodata_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.3: Result with non-flooded areas as NoData</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>polygonize_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\04_vectorization\polygonize_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.4: Polygonize dialog</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>polygonized_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\04_vectorization\polygonized_result.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.4: Vector polygons created from flood raster</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>select_by_location_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\04_vectorization\select_by_location_dialog.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.5: Select by Location dialog for coastline intersection</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>filtered_selection.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\04_vectorization\filtered_selection.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.5: Selected flood areas connected to coastline</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>final_flood_extent.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\04_vectorization\final_flood_extent.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 4.5: Final filtered flood extent</p>
</div>
</div>
</section>
<section id="step-5-create-final-map" class="level2">
<h2 class="anchored" data-anchor-id="step-5-create-final-map">Step 5: Create Final Map</h2>
<p>Create comprehensive map layout with cartographic elements.</p>
<p>Organize layers: flood extent, DEM background, coastline reference.</p>
<p><em>[Screenshot placeholder - save as: 05_quality_control/layer_styling.png]</em></p>
<p>Set up print layout with map frame, legend, scale bar, north arrow.</p>
<p><em>[Screenshot placeholder - save as: 05_quality_control/print_layout_setup.png]</em></p>
<p>Add title and cartographic elements.</p>
<p><em>[Screenshot placeholder - save as: 05_quality_control/map_elements.png]</em></p>
<p>Final map ready for export.</p>
<p><em>[Screenshot placeholder - save as: 06_export/final_map_preview.png]</em></p>
<p>Export options: PDF, PNG, print formats.</p>
<p><em>[Screenshot placeholder - save as: 06_export/export_options.png]</em></p>
<div id="4d16d1b3" class="cell" data-execution_count="6">
<div class="sourceCode cell-code" id="cb5"><pre class="sourceCode python code-with-copy"><code class="sourceCode python"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Display Step 5 screenshots</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"05_quality_control"</span>, <span class="st">"layer_styling.png"</span>,</span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 5: Styling flood extent and background layers"</span>)</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"05_quality_control"</span>, <span class="st">"print_layout_setup.png"</span>,</span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 5: Setting up print layout with map frame"</span>)</span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"05_quality_control"</span>, <span class="st">"map_elements.png"</span>,</span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 5: Adding legend, scale bar, north arrow, and title"</span>)</span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"06_export"</span>, <span class="st">"final_map_preview.png"</span>,</span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 5: Final map layout ready for export"</span>, width<span class="op">=</span><span class="dv">1000</span>)</span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>show_step_image(<span class="st">"06_export"</span>, <span class="st">"export_options.png"</span>,</span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>                <span class="st">"Step 5: Export options for different output formats"</span>)</span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"</span><span class="ch">\n</span><span class="st">"</span> <span class="op">+</span> <span class="st">"="</span><span class="op">*</span><span class="dv">60</span>)</span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"🎯 UN-SPIDER METHODOLOGY COMPLETE"</span>)</span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"="</span><span class="op">*</span><span class="dv">60</span>)</span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"✅ Step 1: Data acquisition and QGIS setup"</span>)</span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"✅ Step 2: DEM preprocessing"</span>)</span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"✅ Step 3: Shoreline preprocessing"</span>)</span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"✅ Step 4: Core flood processing (5 sub-steps)"</span>)</span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"✅ Step 5: Final map creation"</span>)</span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"="</span><span class="op">*</span><span class="dv">60</span>)</span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"📋 DELIVERABLES:"</span>)</span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"   • Binary flood raster"</span>)</span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"   • Vector flood polygons"</span>)</span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"   • Filtered coastal flood extent"</span>)</span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"   • Final cartographic map"</span>)</span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a><span class="bu">print</span>(<span class="st">"="</span><span class="op">*</span><span class="dv">60</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>layer_styling.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\05_quality_control\layer_styling.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 5: Styling flood extent and background layers</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>print_layout_setup.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\05_quality_control\print_layout_setup.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 5: Setting up print layout with map frame</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>map_elements.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\05_quality_control\map_elements.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 5: Adding legend, scale bar, north arrow, and title</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>final_map_preview.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\06_export\final_map_preview.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 5: Final map layout ready for export</p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>⚠️ <strong>Image not found:</strong> <code>export_options.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📝 <strong>Save screenshot as:</strong> <code>c:\AnacondaProjects\04_PREP\Pictures\QGIS_workflow\06_export\export_options.png</code></p>
</div>
<div class="cell-output cell-output-display cell-output-markdown">
<p>📋 <strong>For:</strong> Step 5: Export options for different output formats</p>
</div>
<div class="cell-output cell-output-stdout">
<pre><code>
============================================================
🎯 UN-SPIDER METHODOLOGY COMPLETE
============================================================
✅ Step 1: Data acquisition and QGIS setup
✅ Step 2: DEM preprocessing
✅ Step 3: Shoreline preprocessing
✅ Step 4: Core flood processing (5 sub-steps)
✅ Step 5: Final map creation
============================================================
📋 DELIVERABLES:
   • Binary flood raster
   • Vector flood polygons
   • Filtered coastal flood extent
   • Final cartographic map
============================================================</code></pre>
</div>
</div>
<hr>
</section>
<section id="methodology-summary" class="level2">
<h2 class="anchored" data-anchor-id="methodology-summary">📚 Methodology Summary</h2>
<section id="un-spider-workflow-implementation" class="level3">
<h3 class="anchored" data-anchor-id="un-spider-workflow-implementation">UN-SPIDER Workflow Implementation:</h3>
<p>This template follows the UN-SPIDER Step-by-Step Guide for Digital Elevation Data Storm Surge Coastal Flood Modelling, adapted for Danish sea level rise analysis.</p>
</section>
<section id="processing-chain" class="level3">
<h3 class="anchored" data-anchor-id="processing-chain">Processing Chain:</h3>
<pre><code>Data Acquisition → DEM Preprocessing → Shoreline Preprocessing →
Flood Processing (5 sub-steps) → Final Mapping</code></pre>
</section>
<section id="key-adaptations" class="level3">
<h3 class="anchored" data-anchor-id="key-adaptations">Key Adaptations:</h3>
<ul>
<li><strong>Higher Resolution:</strong> Danish DTM vs.&nbsp;Copernicus 30m DEM</li>
<li><strong>Local CRS:</strong> ETRS89/UTM32N coordinate system</li>
<li><strong>Sea Level Rise:</strong> 0.8m scenario for 2100 projections</li>
<li><strong>Hydrological Enhancement:</strong> Sink filling for connectivity</li>
</ul>
</section>
<section id="deliverables" class="level3">
<h3 class="anchored" data-anchor-id="deliverables">Deliverables:</h3>
<ul>
<li>Binary flood raster</li>
<li>Vector flood polygons</li>
<li>Filtered coastal flood extent</li>
<li>Cartographic map</li>
</ul>
</section>
<section id="alternative-qgis-model-approach" class="level3">
<h3 class="anchored" data-anchor-id="alternative-qgis-model-approach">Alternative: QGIS Model Approach</h3>
<p>For automated processing, the UN-SPIDER QGIS model is available at: <a href="https://github.com/josibregulla/SPEAR---Storm-Surge-Modelling">https://github.com/josibregulla/SPEAR—Storm-Surge-Modelling</a></p>
<hr>
<p><strong>Template ready for documentation!</strong> 📸</p>
<p>Fill in screenshots and observations as you work through each step.</p>

</section>
</section>

<div id="quarto-appendix" class="default"><section class="quarto-appendix-contents" role="doc-bibliography" id="quarto-bibliography"><h2 class="anchored quarto-appendix-heading">References</h2><div id="refs" class="references csl-bib-body hanging-indent" data-entry-spacing="0" role="list">
<div id="ref-kli2025danmarkshojdemodel" class="csl-entry" role="listitem">
Klimadatastyrelsen. 2025. <span>“Danmarks Højdemodel - Terræn.”</span> Data. Dataforsyningen. 2025. <a href="https://dataforsyningen.dk/data/930">https://dataforsyningen.dk/data/930</a>.
</div>
</div></section></div></main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
  window.document.addEventListener("DOMContentLoaded", function (event) {
    const icon = "";
    const anchorJS = new window.AnchorJS();
    anchorJS.options = {
      placement: 'right',
      icon: icon
    };
    anchorJS.add('.anchored');
    const isCodeAnnotation = (el) => {
      for (const clz of el.classList) {
        if (clz.startsWith('code-annotation-')) {                     
          return true;
        }
      }
      return false;
    }
    const onCopySuccess = function(e) {
      // button target
      const button = e.trigger;
      // don't keep focus
      button.blur();
      // flash "checked"
      button.classList.add('code-copy-button-checked');
      var currentTitle = button.getAttribute("title");
      button.setAttribute("title", "Copied!");
      let tooltip;
      if (window.bootstrap) {
        button.setAttribute("data-bs-toggle", "tooltip");
        button.setAttribute("data-bs-placement", "left");
        button.setAttribute("data-bs-title", "Copied!");
        tooltip = new bootstrap.Tooltip(button, 
          { trigger: "manual", 
            customClass: "code-copy-button-tooltip",
            offset: [0, -8]});
        tooltip.show();    
      }
      setTimeout(function() {
        if (tooltip) {
          tooltip.hide();
          button.removeAttribute("data-bs-title");
          button.removeAttribute("data-bs-toggle");
          button.removeAttribute("data-bs-placement");
        }
        button.setAttribute("title", currentTitle);
        button.classList.remove('code-copy-button-checked');
      }, 1000);
      // clear code selection
      e.clearSelection();
    }
    const getTextToCopy = function(trigger) {
        const codeEl = trigger.previousElementSibling.cloneNode(true);
        for (const childEl of codeEl.children) {
          if (isCodeAnnotation(childEl)) {
            childEl.remove();
          }
        }
        return codeEl.innerText;
    }
    const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
      text: getTextToCopy
    });
    clipboard.on('success', onCopySuccess);
    if (window.document.getElementById('quarto-embedded-source-code-modal')) {
      const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
        text: getTextToCopy,
        container: window.document.getElementById('quarto-embedded-source-code-modal')
      });
      clipboardModal.on('success', onCopySuccess);
    }
      var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
      var mailtoRegex = new RegExp(/^mailto:/);
        var filterRegex = new RegExp('/' + window.location.host + '/');
      var isInternal = (href) => {
          return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
      }
      // Inspect non-navigation links and adorn them if external
     var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
      for (var i=0; i<links.length; i++) {
        const link = links[i];
        if (!isInternal(link.href)) {
          // undo the damage that might have been done by quarto-nav.js in the case of
          // links that we want to consider external
          if (link.dataset.originalHref !== undefined) {
            link.href = link.dataset.originalHref;
          }
        }
      }
    function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
      const config = {
        allowHTML: true,
        maxWidth: 500,
        delay: 100,
        arrow: false,
        appendTo: function(el) {
            return el.parentElement;
        },
        interactive: true,
        interactiveBorder: 10,
        theme: 'quarto',
        placement: 'bottom-start',
      };
      if (contentFn) {
        config.content = contentFn;
      }
      if (onTriggerFn) {
        config.onTrigger = onTriggerFn;
      }
      if (onUntriggerFn) {
        config.onUntrigger = onUntriggerFn;
      }
      window.tippy(el, config); 
    }
    const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
    for (var i=0; i<noterefs.length; i++) {
      const ref = noterefs[i];
      tippyHover(ref, function() {
        // use id or data attribute instead here
        let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
        try { href = new URL(href).hash; } catch {}
        const id = href.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note) {
          return note.innerHTML;
        } else {
          return "";
        }
      });
    }
    const xrefs = window.document.querySelectorAll('a.quarto-xref');
    const processXRef = (id, note) => {
      // Strip column container classes
      const stripColumnClz = (el) => {
        el.classList.remove("page-full", "page-columns");
        if (el.children) {
          for (const child of el.children) {
            stripColumnClz(child);
          }
        }
      }
      stripColumnClz(note)
      if (id === null || id.startsWith('sec-')) {
        // Special case sections, only their first couple elements
        const container = document.createElement("div");
        if (note.children && note.children.length > 2) {
          container.appendChild(note.children[0].cloneNode(true));
          for (let i = 1; i < note.children.length; i++) {
            const child = note.children[i];
            if (child.tagName === "P" && child.innerText === "") {
              continue;
            } else {
              container.appendChild(child.cloneNode(true));
              break;
            }
          }
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(container);
          }
          return container.innerHTML
        } else {
          if (window.Quarto?.typesetMath) {
            window.Quarto.typesetMath(note);
          }
          return note.innerHTML;
        }
      } else {
        // Remove any anchor links if they are present
        const anchorLink = note.querySelector('a.anchorjs-link');
        if (anchorLink) {
          anchorLink.remove();
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        if (note.classList.contains("callout")) {
          return note.outerHTML;
        } else {
          return note.innerHTML;
        }
      }
    }
    for (var i=0; i<xrefs.length; i++) {
      const xref = xrefs[i];
      tippyHover(xref, undefined, function(instance) {
        instance.disable();
        let url = xref.getAttribute('href');
        let hash = undefined; 
        if (url.startsWith('#')) {
          hash = url;
        } else {
          try { hash = new URL(url).hash; } catch {}
        }
        if (hash) {
          const id = hash.replace(/^#\/?/, "");
          const note = window.document.getElementById(id);
          if (note !== null) {
            try {
              const html = processXRef(id, note.cloneNode(true));
              instance.setContent(html);
            } finally {
              instance.enable();
              instance.show();
            }
          } else {
            // See if we can fetch this
            fetch(url.split('#')[0])
            .then(res => res.text())
            .then(html => {
              const parser = new DOMParser();
              const htmlDoc = parser.parseFromString(html, "text/html");
              const note = htmlDoc.getElementById(id);
              if (note !== null) {
                const html = processXRef(id, note);
                instance.setContent(html);
              } 
            }).finally(() => {
              instance.enable();
              instance.show();
            });
          }
        } else {
          // See if we can fetch a full url (with no hash to target)
          // This is a special case and we should probably do some content thinning / targeting
          fetch(url)
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.querySelector('main.content');
            if (note !== null) {
              // This should only happen for chapter cross references
              // (since there is no id in the URL)
              // remove the first header
              if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
                note.children[0].remove();
              }
              const html = processXRef(null, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      }, function(instance) {
      });
    }
        let selectedAnnoteEl;
        const selectorForAnnotation = ( cell, annotation) => {
          let cellAttr = 'data-code-cell="' + cell + '"';
          let lineAttr = 'data-code-annotation="' +  annotation + '"';
          const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
          return selector;
        }
        const selectCodeLines = (annoteEl) => {
          const doc = window.document;
          const targetCell = annoteEl.getAttribute("data-target-cell");
          const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
          const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
          const lines = annoteSpan.getAttribute("data-code-lines").split(",");
          const lineIds = lines.map((line) => {
            return targetCell + "-" + line;
          })
          let top = null;
          let height = null;
          let parent = null;
          if (lineIds.length > 0) {
              //compute the position of the single el (top and bottom and make a div)
              const el = window.document.getElementById(lineIds[0]);
              top = el.offsetTop;
              height = el.offsetHeight;
              parent = el.parentElement.parentElement;
            if (lineIds.length > 1) {
              const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
              const bottom = lastEl.offsetTop + lastEl.offsetHeight;
              height = bottom - top;
            }
            if (top !== null && height !== null && parent !== null) {
              // cook up a div (if necessary) and position it 
              let div = window.document.getElementById("code-annotation-line-highlight");
              if (div === null) {
                div = window.document.createElement("div");
                div.setAttribute("id", "code-annotation-line-highlight");
                div.style.position = 'absolute';
                parent.appendChild(div);
              }
              div.style.top = top - 2 + "px";
              div.style.height = height + 4 + "px";
              div.style.left = 0;
              let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
              if (gutterDiv === null) {
                gutterDiv = window.document.createElement("div");
                gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
                gutterDiv.style.position = 'absolute';
                const codeCell = window.document.getElementById(targetCell);
                const gutter = codeCell.querySelector('.code-annotation-gutter');
                gutter.appendChild(gutterDiv);
              }
              gutterDiv.style.top = top - 2 + "px";
              gutterDiv.style.height = height + 4 + "px";
            }
            selectedAnnoteEl = annoteEl;
          }
        };
        const unselectCodeLines = () => {
          const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
          elementsIds.forEach((elId) => {
            const div = window.document.getElementById(elId);
            if (div) {
              div.remove();
            }
          });
          selectedAnnoteEl = undefined;
        };
          // Handle positioning of the toggle
      window.addEventListener(
        "resize",
        throttle(() => {
          elRect = undefined;
          if (selectedAnnoteEl) {
            selectCodeLines(selectedAnnoteEl);
          }
        }, 10)
      );
      function throttle(fn, ms) {
      let throttle = false;
      let timer;
        return (...args) => {
          if(!throttle) { // first call gets through
              fn.apply(this, args);
              throttle = true;
          } else { // all the others get throttled
              if(timer) clearTimeout(timer); // cancel #2
              timer = setTimeout(() => {
                fn.apply(this, args);
                timer = throttle = false;
              }, ms);
          }
        };
      }
        // Attach click handler to the DT
        const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
        for (const annoteDlNode of annoteDls) {
          annoteDlNode.addEventListener('click', (event) => {
            const clickedEl = event.target;
            if (clickedEl !== selectedAnnoteEl) {
              unselectCodeLines();
              const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
              if (activeEl) {
                activeEl.classList.remove('code-annotation-active');
              }
              selectCodeLines(clickedEl);
              clickedEl.classList.add('code-annotation-active');
            } else {
              // Unselect the line
              unselectCodeLines();
              clickedEl.classList.remove('code-annotation-active');
            }
          });
        }
    const findCites = (el) => {
      const parentEl = el.parentElement;
      if (parentEl) {
        const cites = parentEl.dataset.cites;
        if (cites) {
          return {
            el,
            cites: cites.split(' ')
          };
        } else {
          return findCites(el.parentElement)
        }
      } else {
        return undefined;
      }
    };
    var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
    for (var i=0; i<bibliorefs.length; i++) {
      const ref = bibliorefs[i];
      const citeInfo = findCites(ref);
      if (citeInfo) {
        tippyHover(citeInfo.el, function() {
          var popup = window.document.createElement('div');
          citeInfo.cites.forEach(function(cite) {
            var citeDiv = window.document.createElement('div');
            citeDiv.classList.add('hanging-indent');
            citeDiv.classList.add('csl-entry');
            var biblioDiv = window.document.getElementById('ref-' + cite);
            if (biblioDiv) {
              citeDiv.innerHTML = biblioDiv.innerHTML;
            }
            popup.appendChild(citeDiv);
          });
          return popup.innerHTML;
        });
      }
    }
  });
  </script>
</div> <!-- /content -->




</body></html>